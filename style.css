/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    /* منع التمرير الأفقي */
    overflow-x: hidden;
    /* ضمان ملء الشاشة بالكامل */
    width: 100%;
    height: 100%;
    /* تحسين العرض على الهواتف */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    /* منع التكبير على الهواتف */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
    /* منع التمرير الأفقي */
    overflow-x: hidden;
    /* ضمان ملء الشاشة بالكامل */
    width: 100%;
    min-height: 100vh;
    /* تحسين العرض على الهواتف */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    /* ضمان عدم تجاوز عرض الشاشة */
    width: 100%;
    box-sizing: border-box;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo i {
    font-size: 2rem;
    color: #3b82f6;
}

.logo span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937;
}

.nav-desktop {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #4b5563;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 0;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #3b82f6;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0;
    height: 2px;
    background: #3b82f6;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #4b5563;
    cursor: pointer;
}

.nav-mobile {
    display: none;
    position: absolute;
    top: 70px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
}

.nav-mobile.active {
    display: flex;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #f0fdfa 100%);
    position: relative;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    inset: 0;
    opacity: 0.05;
}

.hero-bg::before,
.hero-bg::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: #3b82f6;
    filter: blur(60px);
    animation: float 6s ease-in-out infinite;
}

.hero-bg::before {
    width: 300px;
    height: 300px;
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.hero-bg::after {
    width: 250px;
    height: 250px;
    bottom: 20%;
    left: 20%;
    background: #10b981;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-content {
    text-align: center;
    max-width: 800px;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.highlight {
    color: #3b82f6;
    display: block;
    margin-top: 0.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.feature-highlights {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-weight: 500;
    color: #4b5563;
}

.feature-item i {
    color: #3b82f6;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: #3b82f6;
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #4b5563;
    border: 2px solid #d1d5db;
}

.btn-secondary:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.trust-indicators {
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.trust-text {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    opacity: 0.6;
}

.stat-item {
    font-size: 1.5rem;
    font-weight: bold;
    color: #9ca3af;
}

.divider {
    width: 1px;
    height: 2rem;
    background: #d1d5db;
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: #f9fafb;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.service-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.service-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}
.icon-ser{
   
    align-items: center;
    justify-content: space-between;
}



.service-card:hover .service-icon {
    background: #3b82f6;
    color: white;
}

.service-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.25rem;
    align-items: center;
    padding: 1px 90px;
}
.service-titl2{
    font-size: 1.25rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.25rem;
    align-items: center;
    padding: 1px 90px;
}

.service-price {
    color: #3b82f6;
    font-weight: 600;
    padding: 1px 125px;
    width: 50
}

.service-description {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features h4 {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.service-features ul {
    list-style: none;
    margin-bottom: 1.5rem;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.service-features i {
    color: #10b981;
    font-size: 0.8rem;
}

.service-btn {
    width: 100%;
    justify-content: center;
}

.why-choose-us {
    margin-top: 4rem;
}

.why-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.why-card h3 {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
}

.stats-grid .stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-grid .stat-item:nth-child(1) .stat-number {
    color: #3b82f6;
}

.stats-grid .stat-item:nth-child(2) .stat-number {
    color: #10b981;
}

.stats-grid .stat-item:nth-child(3) .stat-number {
    color: #f59e0b;
}

.stats-grid .stat-item p {
    color: #6b7280;
    font-size: 1rem;
}

/* Portfolio Section */
.portfolio {
    padding: 5rem 0;
    background: white;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.portfolio-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 250px;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-image {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.portfolio-placeholder {
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.8);
}

.portfolio-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay h4 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.portfolio-overlay p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.portfolio-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.portfolio-tags span {
    background: rgba(59, 130, 246, 0.8);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.portfolio-cta {
    text-align: center;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(59, 130, 246, 0.3);
}

.portfolio-cta h3 {
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.portfolio-cta p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.portfolio-cta .btn {
    background: white;
    color: #3b82f6;
}

.portfolio-cta .btn:hover {
    background: #f8fafc;
    transform: translateY(-2px);
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #f9fafb;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-form-container {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-submit {
    margin-top: 1rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-icon {
    background: #eff6ff;
    color: #3b82f6;
    padding: 1rem;
    border-radius: 10px;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.contact-details h4 {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.contact-details p,
.contact-details a {
    color: #6b7280;
    margin-bottom: 0.25rem;
    text-decoration: none;
}

.contact-details a:hover {
    color: #3b82f6;
}

.social-media {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.social-media h4 {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
    width: auto;
    
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-logo i {
    font-size: 1.5rem;
    color: #3b82f6;
}

.footer-logo span {
    font-size: 1.25rem;
    font-weight: bold;
}

.footer-description {
    color: #9ca3af;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-social {
    display: flex;
    gap: 0.75rem;
}

.footer-social .social-link {
    width: 40px;
    height: 40px;
    background: #374151;
}

.footer-social .social-link:hover {
    background: #3b82f6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #3b82f6;
}

.footer-contact p {
    color: #9ca3af;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-contact i {
    color: #3b82f6;
    width: 16px;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-copyright p {
    color: #9ca3af;
    margin: 0;
}

.footer-links-bottom {
    display: flex;
    gap: 2rem;
}

.footer-links-bottom a {
    color: #9ca3af;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-links-bottom a:hover {
    color: #3b82f6;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .container {
        max-width: 95%;
        padding: 0 15px;
    }

    .services-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.8rem;
    }
}

@media (max-width: 1200px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .hero-title {
        font-size: 3.5rem;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .section-title {
        font-size: 2.2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }
    
    .feature-highlights {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    /* ضمان ملء الشاشة بالكامل */
    html, body {
        width: 100%;
        overflow-x: hidden;
    }

    .container {
        width: 100%;
        max-width: 100%;
        padding: 0 10px;
        margin: 0;
    }

    .nav-desktop {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero {
        padding: 80px 0 40px;
        min-height: 100vh;
        width: 100%;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .feature-highlights {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
        margin-bottom: 2rem;
    }

    .feature-item {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
        margin-bottom: 2rem;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        text-align: center;
        justify-content: center;
    }

    .stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .divider {
        display: none;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 1.5rem;
        text-align: center;
    }

    .icon-ser {
        width: 100%;
        max-width: 250px;
        height: auto;
    }
    
    .service-title, .service-titl2 {
        padding: 10px 0;
        font-size: 1.1rem;
        text-align: center;
    }
    
    .service-price {
        padding: 5px 0;
        text-align: center;
        font-size: 1.1rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    /* ضمان ملء الشاشة بالكامل للهواتف الصغيرة */
    html, body {
        width: 100vw;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    .container {
        width: 100%;
        max-width: 100vw;
        padding: 0 8px;
        margin: 0;
        box-sizing: border-box;
    }

    .nav-wrapper {
        height: 60px;
        padding: 0 8px;
        width: 100%;
    }

    .logo span {
        font-size: 1.2rem;
    }

    .hero {
        padding: 70px 0 30px;
        width: 100%;
        min-height: 100vh;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 0.95rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .service-card {
        padding: 1.2rem;
    }

    .icon-ser {
        max-width: 200px;
    }
    
    .service-title, .service-titl2 {
        font-size: 1rem;
        padding: 8px 0;
    }
    
    .service-price {
        font-size: 1rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .contact-form-container {
        padding: 1.5rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 1rem;
    }
}

@media (max-width: 360px) {
    /* تحسينات خاصة للهواتف الصغيرة جداً */
    html, body {
        width: 100vw;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    .container {
        width: 100%;
        max-width: 100vw;
        padding: 0 5px;
        margin: 0;
        box-sizing: border-box;
    }

    .hero-title {
        font-size: 1.6rem;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .service-card {
        padding: 1rem;
        width: 100%;
        max-width: 100%;
    }

    .icon-ser {
        max-width: 180px;
    }

    .service-title, .service-titl2 {
        font-size: 0.95rem;
        padding: 5px 0;
    }

    .service-price {
        font-size: 0.95rem;
    }

    .btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
        width: 100%;
        max-width: 100%;
    }

    .feature-item {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        width: 100%;
        max-width: 100%;
    }
}

@media (max-width: 320px) {
    /* تحسينات للهواتف الصغيرة جداً */
    html, body {
        width: 100vw;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .container {
        width: 100%;
        max-width: 100vw;
        padding: 0 3px;
        margin: 0;
        box-sizing: border-box;
    }

    .hero {
        width: 100%;
        min-height: 100vh;
        padding: 60px 0 20px;
    }

    .hero-title {
        font-size: 1.4rem;
        line-height: 1.2;
    }

    .service-card {
        width: 100%;
        max-width: 100%;
        padding: 0.8rem;
        margin: 0;
    }

    .service-title, .service-titl2 {
        padding: 5px 0;
        font-size: 0.9rem;
        text-align: center;
        width: 100%;
    }

    .service-price {
        padding: 5px 0;
        text-align: center;
        width: 100%;
    }

    .btn {
        width: 100%;
        max-width: 100%;
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
        text-align: center;
    }

    .feature-item {
        width: 100%;
        max-width: 100%;
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
        text-align: center;
    }
}

/* Mobile First Approach */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
        max-width: 100%;
    }

    .nav-wrapper {
        height: 60px;
        padding: 0 10px;
    }

    .logo {
        gap: 5px;
    }

    .logo img {
        width: 50px;
        height: 50px;
    }

    .logo span {
        font-size: 1.1rem;
    }

    .nav-desktop {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
        font-size: 1.2rem;
    }

    .hero {
        padding: 70px 0 30px;
        min-height: 85vh;
    }

    .hero-title {
        font-size: 1.8rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .feature-highlights {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .feature-item {
        width: 90%;
        max-width: 250px;
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        justify-content: center;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 0.8rem;
        margin-bottom: 2rem;
    }

    .btn {
        width: 90%;
        max-width: 250px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        text-align: center;
        justify-content: center;
    }

    .trust-indicators {
        padding-top: 1.5rem;
    }

    .stats {
        flex-direction: column;
        gap: 0.8rem;
        text-align: center;
    }

    .stat-item {
        font-size: 1.2rem;
    }

    .divider {
        display: none;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 0.8rem;
    }

    .section-subtitle {
        font-size: 0.95rem;
        padding: 0 10px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .service-card {
        padding: 1.2rem;
        text-align: center;
        margin: 0 auto;
        max-width: 320px;
    }

    .service-header {
        flex-direction: column;
        text-align: center;
        gap: 0.8rem;
    }

    .icon-ser {
        width: 100%;
        max-width: 200px;
        height: auto;
        margin: 0 auto;
    }

    .service-title, .service-titl2 {
        padding: 8px 0;
        font-size: 1rem;
        text-align: center;
        width: 100%;
    }

    .service-price {
        padding: 5px 0;
        text-align: center;
        font-size: 1rem;
        width: 100%;
    }

    .service-description {
        font-size: 0.9rem;
        text-align: center;
        margin: 1rem 0;
    }

    .service-features {
        text-align: right;
        margin: 1rem 0;
    }

    .service-features h4 {
        text-align: center;
        margin-bottom: 0.8rem;
    }

    .service-features ul {
        max-width: 280px;
        margin: 0 auto;
    }

    .service-features li {
        font-size: 0.85rem;
        margin-bottom: 0.4rem;
        text-align: right;
    }

    .service-btn {
        width: 100%;
        max-width: 200px;
        margin: 1rem auto 0;
        display: block;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-form-container {
        padding: 1.2rem;
        order: 2;
    }

    .contact-info {
        order: 1;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 1rem;
        width: 100%;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 8px;
    }

    .hero-title {
        font-size: 1.6rem;
    }

    .hero-subtitle {
        font-size: 0.85rem;
    }

    .feature-item {
        width: 95%;
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .btn {
        width: 95%;
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }

    .service-card {
        padding: 1rem;
        max-width: 300px;
    }

    .icon-ser {
        max-width: 180px;
    }

    .service-title, .service-titl2 {
        font-size: 0.95rem;
        padding: 5px 0;
    }

    .service-price {
        font-size: 0.95rem;
    }

    .section-title {
        font-size: 1.6rem;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 0 5px;
    }

    .hero-title {
        font-size: 1.4rem;
    }

    .service-card {
        padding: 0.8rem;
        max-width: 280px;
    }

    .icon-ser {
        max-width: 160px;
    }

    .service-title, .service-titl2 {
        font-size: 0.9rem;
        padding: 3px 0;
    }

    .service-price {
        font-size: 0.9rem;
    }

    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
}

/* تحسينات إضافية للشاشات الصغيرة */
@media (max-width: 350px) {
    .hero-title {
        font-size: 1.2rem;
    }

    .hero-subtitle {
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .service-card {
        padding: 0.5rem;
    }

    .icon-ser {
        max-width: 80px;
    }

    .service-title,
    .service-titl2 {
        font-size: 0.8rem;
    }

    .service-price {
        font-size: 0.85rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
        min-height: 32px;
    }

    .feature-item {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
        min-height: 28px;
    }

    .contact-form-container {
        padding: 0.5rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.4rem;
        font-size: 0.7rem;
    }

    .floating-whatsapp {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.9rem !important;
    }

    .scroll-to-top {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.8rem !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 320px) {
    .container {
        padding: 0 5px;
    }

    .hero-title {
        font-size: 1.1rem;
    }

    .hero-subtitle {
        font-size: 0.75rem;
    }

    .section-title {
        font-size: 1rem;
    }

    .service-card {
        padding: 0.4rem;
        max-width: 280px;
    }

    .icon-ser {
        max-width: 70px;
    }

    .service-title,
    .service-titl2 {
        font-size: 0.75rem;
        padding: 2px 0;
    }

    .service-price {
        font-size: 0.8rem;
    }

    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
        min-height: 28px;
    }

    .feature-item {
        padding: 0.25rem 0.5rem;
        font-size: 0.6rem;
        min-height: 24px;
    }

    .floating-whatsapp {
        width: 30px !important;
        height: 30px !important;
        font-size: 0.8rem !important;
        bottom: 10px !important;
        left: 10px !important;
    }

    .scroll-to-top {
        width: 30px !important;
        height: 30px !important;
        font-size: 0.7rem !important;
        bottom: 10px !important;
        right: 10px !important;
    }
}

    

/* تحسينات إضافية للشاشات الصغيرة */
@media (max-width: 350px) {
    .hero-title {
        font-size: 1.2rem;
    }

    .hero-subtitle {
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .service-card {
        padding: 0.5rem;
    }

    .icon-ser {
        max-width: 80px;
    }

    .service-title,
    .service-titl2 {
        font-size: 0.8rem;
    }

    .service-price {
        font-size: 0.85rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
        min-height: 32px;
    }

    .feature-item {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
        min-height: 28px;
    }

    .contact-form-container {
        padding: 0.5rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.4rem;
        font-size: 0.7rem;
    }

    .floating-whatsapp {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.9rem !important;
    }

    .scroll-to-top {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.8rem !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 320px) {
    .container {
        padding: 0 5px;
    }

    .hero-title {
        font-size: 1.1rem;
    }

    .hero-subtitle {
        font-size: 0.75rem;
    }

    .section-title {
        font-size: 1rem;
    }

    .service-card {
        padding: 0.4rem;
        max-width: 280px;
    }

    .icon-ser {
        max-width: 70px;
    }

    .service-title,
    .service-titl2 {
        font-size: 0.75rem;
        padding: 2px 0;
    }

    .service-price {
        font-size: 0.8rem;
    }

    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
        min-height: 28px;
    }

    .feature-item {
        padding: 0.25rem 0.5rem;
        font-size: 0.6rem;
        min-height: 24px;
    }

    .floating-whatsapp {
        width: 30px !important;
        height: 30px !important;
        font-size: 0.8rem !important;
        bottom: 10px !important;
        left: 10px !important;
    }

    .scroll-to-top {
        width: 30px !important;
        height: 30px !important;
        font-size: 0.7rem !important;
        bottom: 10px !important;
        right: 10px !important;
    }
}

/* تحسينات عامة لجميع الأجهزة المحمولة - ضمان ملء الشاشة */
@media screen and (max-width: 768px) {
    /* ضمان ملء الشاشة بالكامل */
    * {
        box-sizing: border-box;
    }

    html, body {
        width: 100vw;
        max-width: 100vw;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    .container {
        width: 100%;
        max-width: 100vw;
        padding: 0 10px;
        margin: 0 auto;
        box-sizing: border-box;
    }

    /* ضمان أن جميع العناصر لا تتجاوز عرض الشاشة */
    .hero, .services, .portfolio, .contact, .footer {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }

    /* تحسين الصور والأيقونات */
    img, .icon-ser {
        max-width: 100%;
        height: auto;
    }

    /* تحسين الأزرار */
    .btn {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
        display: block;
        text-align: center;
    }

    /* تحسين النصوص */
    .hero-title, .section-title {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
}

/* تحسينات خاصة للهواتف الصغيرة جداً */
@media screen and (max-width: 375px) {
    html, body {
        width: 100vw;
        max-width: 100vw;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .container {
        width: 100%;
        max-width: 100vw;
        padding: 0 5px;
        margin: 0;
        box-sizing: border-box;
    }

    .hero {
        width: 100%;
        min-height: 100vh;
        padding: 60px 0 20px;
    }

    .service-card {
        width: 100%;
        max-width: 100%;
        padding: 0.8rem;
        margin: 0 auto;
    }

    .btn, .feature-item {
        width: 100%;
        max-width: 100%;
        text-align: center;
    }
}

/* إصلاح مشاكل التمرير الأفقي */
@media screen and (max-width: 480px) {
    body {
        overflow-x: hidden;
        width: 100vw;
        max-width: 100vw;
    }

    .container, .hero, .services, .portfolio, .contact, .footer {
        overflow-x: hidden;
        width: 100%;
        max-width: 100vw;
    }

    /* إخفاء أي عناصر قد تسبب تمرير أفقي */
    * {
        max-width: 100vw;
        box-sizing: border-box;
    }

    /* تحسين خاص للعناصر التي قد تتجاوز الشاشة */
    .service-title, .service-titl2, .service-price {
        width: 100%;
        max-width: 100%;
        padding-left: 0;
        padding-right: 0;
        text-align: center;
    }

    .feature-highlights {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }

    .cta-buttons {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

/* تحسينات خاصة للهواتف الذكية الحديثة */
@media screen and (max-width: 414px) {
    html {
        font-size: 14px; /* تقليل حجم الخط الأساسي */
    }

    .hero {
        min-height: 100vh;
        padding: 70px 5px 30px;
    }

    .hero-content {
        padding: 0 5px;
        width: 100%;
        max-width: 100%;
    }

    .services, .portfolio, .contact {
        padding: 3rem 0;
    }

    .section-header {
        padding: 0 10px;
        margin-bottom: 2rem;
    }
}

/* تحسينات للهواتف الصغيرة مثل iPhone SE */
@media screen and (max-width: 375px) and (max-height: 667px) {
    .hero {
        min-height: 100vh;
        padding: 60px 3px 20px;
    }

    .hero-title {
        font-size: 1.5rem;
        line-height: 1.3;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .feature-item {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        margin: 0.2rem 0;
    }

    .btn {
        padding: 0.7rem 1rem;
        font-size: 0.85rem;
        margin: 0.3rem 0;
    }
}

/* تحسينات نهائية لضمان ملء الشاشة على جميع الأجهزة */
@media screen and (orientation: portrait) and (max-width: 768px) {
    /* ضمان ملء الشاشة في الوضع العمودي */
    html, body {
        width: 100vw;
        max-width: 100vw;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .container {
        width: 100%;
        max-width: 100vw;
        padding: 0 8px;
        margin: 0;
        box-sizing: border-box;
    }

    /* تحسين العناصر الرئيسية */
    .hero, .services, .portfolio, .contact, .footer {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    /* تحسين الشبكات */
    .services-grid, .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        width: 100%;
        max-width: 100%;
    }

    /* تحسين البطاقات */
    .service-card, .portfolio-item {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
    }

    /* تحسين النماذج */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        width: 100%;
        max-width: 100%;
    }

    .contact-form-container, .contact-info {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media screen and (max-width: 320px) {
    html {
        font-size: 12px; /* تقليل حجم الخط أكثر */
    }

    .container {
        padding: 0 3px;
    }

    .hero {
        padding: 50px 0 20px;
        min-height: 100vh;
    }

    .hero-title {
        font-size: 1.3rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 1.4rem;
    }

    .service-card {
        padding: 0.6rem;
    }

    .btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
    }

    .feature-item {
        padding: 0.3rem 0.5rem;
        font-size: 0.7rem;
    }
}

/* إصلاح نهائي لمنع التمرير الأفقي */
@media screen and (max-width: 768px) {
    body {
        overflow-x: hidden !important;
        width: 100vw !important;
        max-width: 100vw !important;
    }

    * {
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    .container, .hero, .services, .portfolio, .contact, .footer,
    .service-card, .portfolio-item, .contact-form-container, .contact-info {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100vw !important;
    }
}


// تهيئة الموقع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الموقع بنجاح');
    
    // تهيئة القائمة المحمولة
    initMobileMenu();
    
    // تهيئة التنقل السلس
    initSmoothScroll();
    
    // تهيئة النموذج
    initContactForm();
    
    // تهيئة أزرار الخدمات
    initServiceButtons();
    
    // إضافة أزرار واتساب والعودة للأعلى
    addFloatingButtons();
});

// تهيئة القائمة المحمولة
function initMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileNav = document.getElementById('mobileNav');
    
    if (mobileMenuBtn && mobileNav) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileNav.classList.toggle('active');
            const icon = this.querySelector('i');
            
            if (mobileNav.classList.contains('active')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });
        
        // إغلاق القائمة عند النقر على رابط
        const mobileLinks = mobileNav.querySelectorAll('.nav-link');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileNav.classList.remove('active');
                mobileMenuBtn.querySelector('i').className = 'fas fa-bars';
            });
        });
    }
}

// تهيئة التنقل السلس
function initSmoothScroll() {
    // روابط التنقل
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
    
    // أزرار التمرير
    const scrollButtons = document.querySelectorAll('[data-scroll]');
    scrollButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-scroll');
            scrollToSection(targetId);
        });
    });
}

// دالة التمرير السلس
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const headerHeight = 70;
        const elementPosition = element.offsetTop - headerHeight;
        
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }
}

// تهيئة أزرار الخدمات
function initServiceButtons() {
    const serviceButtons = document.querySelectorAll('[data-service]');
    serviceButtons.forEach(button => {
        button.addEventListener('click', function() {
            const serviceName = this.getAttribute('data-service');
            selectService(serviceName);
        });
    });
}

// اختيار خدمة
function selectService(serviceName) {
    // التمرير لقسم التواصل
    scrollToSection('contact');
    
    // تعبئة الخدمة في النموذج
    setTimeout(() => {
        const serviceSelect = document.getElementById('service');
        if (serviceSelect) {
            serviceSelect.value = serviceName;
        }
        
        // إظهار إشعار
        showNotification(`تم اختيار خدمة: ${serviceName}`);
    }, 500);
}

// تهيئة نموذج التواصل
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmit(this);
        });
    }
}

// معالجة إرسال النموذج
function handleFormSubmit(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value.trim();
    }
    
    // التحقق من البيانات
    if (!data.name || !data.email || !data.message) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إرسال رسالة واتساب
    sendWhatsAppMessage(data);
    
    // إظهار رسالة نجاح
    showNotification('تم إرسال رسالتك بنجاح!', 'success');
    
    // إعادة تعيين النموذج
    form.reset();
}

// إرسال رسالة واتساب
function sendWhatsAppMessage(data) {
    const phoneNumber = '212782297888';
    const message = `مرحباً! أنا مهتم بخدمات تطوير المواقع

الاسم: ${data.name}
البريد الإلكتروني: ${data.email}
الهاتف: ${data.phone || 'غير محدد'}
الخدمة: ${data.service || 'غير محددة'}

الرسالة:
${data.message}

شكراً لكم!`;

    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    
    window.open(whatsappURL, '_blank');
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // إنشاء إشعار جديد
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // تطبيق الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
    `;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إضافة حدث الإغلاق
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());
    
    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// إضافة الأزرار العائمة
function addFloatingButtons() {
    // زر واتساب
    const whatsappBtn = document.createElement('a');
    whatsappBtn.href = 'https://wa.me/212782297888?text=مرحباً! أنا مهتم بخدمات تطوير المواقع';
    whatsappBtn.target = '_blank';
    whatsappBtn.className = 'floating-whatsapp';
    whatsappBtn.innerHTML = '<i class="fab fa-whatsapp"></i>';
    
    whatsappBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #25d366;
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        text-decoration: none;
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(whatsappBtn);
    
    // زر العودة للأعلى
    const scrollTopBtn = document.createElement('button');
    scrollTopBtn.className = 'scroll-to-top';
    scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollTopBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #3b82f6;
        color: white;
        width: 50px;
        height: 50px;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        z-index: 1000;
        transition: all 0.3s ease;
        opacity: 0;
        transform: translateY(20px);
    `;
    
    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
    
    document.body.appendChild(scrollTopBtn);
    
    // إظهار/إخفاء زر العودة للأعلى
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollTopBtn.style.opacity = '1';
            scrollTopBtn.style.transform = 'translateY(0)';
        } else {
            scrollTopBtn.style.opacity = '0';
            scrollTopBtn.style.transform = 'translateY(20px)';
        }
    });
}

// إضافة أنماط الإشعارات
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0.25rem;
        margin-right: auto;
    }
`;
document.head.appendChild(notificationStyles);


